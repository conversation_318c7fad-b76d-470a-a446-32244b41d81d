# MCP-Chrome Setup and Configuration Guide for Augment Code

## Overview

This guide documents the complete installation, configuration, and testing of the mcp-chrome project for use with Augment Code. The mcp-chrome project is a Chrome extension-based Model Context Protocol (MCP) server that exposes Chrome browser functionality to AI assistants, enabling complex browser automation, content analysis, and semantic search.

## What is MCP-Chrome?

MCP-Chrome is a Chrome extension-based MCP server that:
- Allows AI assistants to control your Chrome browser directly
- Uses your existing browser environment (login states, configurations, etc.)
- Provides 20+ tools for browser automation, screenshots, network monitoring, and more
- Runs locally ensuring privacy
- Supports semantic search with built-in vector database
- Uses SIMD-accelerated AI for faster vector operations

## Installation Summary

### Prerequisites Met
- ✅ Node.js v20.19.4 installed
- ✅ npm v10.8.2 available
- ✅ Git installed
- ✅ Chrome/Chromium browser available

### Components Installed

#### 1. MCP-Chrome Bridge Server
- **Package**: `mcp-chrome-bridge@1.0.29`
- **Installation**: Global npm package
- **Location**: `/home/<USER>/.nvm/versions/node/v20.19.4/lib/node_modules/mcp-chrome-bridge/`
- **Status**: ✅ Running (PID: 52496)
- **Endpoint**: `http://127.0.0.1:12306/mcp`

#### 2. Chrome Extension
- **Version**: v0.0.6 (latest)
- **Source**: Downloaded from GitHub repository
- **Location**: `/home/<USER>/Desktop/VS_Code/mcp-chrome-extension/`
- **Status**: ✅ Ready for installation in Chrome

## Configuration for Augment Code

### MCP Server Configuration

The following configuration should be added to your Augment Code MCP servers configuration:

```json
{
  "mcpServers": {
    "streamable-mcp-server": {
      "type": "streamable-http",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

**Note**: This matches the configuration you mentioned was already provided by the Chrome extension.

### Alternative STDIO Configuration

If your client requires STDIO connection instead of HTTP:

```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "npx",
      "args": [
        "node",
        "/home/<USER>/.nvm/versions/node/v20.19.4/lib/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
      ]
    }
  }
}
```

## Chrome Extension Installation

### Manual Installation Steps

1. **Open Chrome Extensions Page**:
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right corner)

2. **Load the Extension**:
   - Click "Load unpacked"
   - Select the directory: `/home/<USER>/Desktop/VS_Code/mcp-chrome-extension/`
   - The extension should appear in your extensions list

3. **Activate the Extension**:
   - Click the extension icon in Chrome toolbar
   - Click "Connect" to establish connection with the MCP server
   - Verify connection status shows as connected

## Available Tools and Features

The mcp-chrome server provides 20+ tools organized into categories:

### 📊 Browser Management (6 tools)
- `get_windows_and_tabs` - List all browser windows and tabs
- `chrome_navigate` - Navigate to URLs and control viewport
- `chrome_close_tabs` - Close specific tabs or windows
- `chrome_go_back_or_forward` - Browser navigation control
- `chrome_inject_script` - Inject content scripts into web pages
- `chrome_send_command_to_inject_script` - Send commands to injected scripts

### 📸 Screenshots & Visual (1 tool)
- `chrome_screenshot` - Advanced screenshot capture with element targeting

### 🌐 Network Monitoring (4 tools)
- `chrome_network_capture_start/stop` - webRequest API network capture
- `chrome_network_debugger_start/stop` - Debugger API with response bodies
- `chrome_network_request` - Send custom HTTP requests

### 🔍 Content Analysis (4 tools)
- `search_tabs_content` - AI-powered semantic search across browser tabs
- `chrome_get_web_content` - Extract HTML/text content from pages
- `chrome_get_interactive_elements` - Find clickable elements
- `chrome_console` - Capture and retrieve console output

### 🎯 Interaction (3 tools)
- `chrome_click_element` - Click elements using CSS selectors
- `chrome_fill_or_select` - Fill forms and select options
- `chrome_keyboard` - Simulate keyboard input and shortcuts

### 📚 Data Management (5 tools)
- `chrome_history` - Search browser history with time filters
- `chrome_bookmark_search` - Find bookmarks by keywords
- `chrome_bookmark_add` - Add new bookmarks with folder support
- `chrome_bookmark_delete` - Delete bookmarks

## Testing and Validation

### Server Status Verification
- ✅ MCP bridge server is running on port 12306
- ✅ Server responds to HTTP requests
- ✅ Process ID: 52496 (can be monitored with `ps aux | grep mcp-chrome-bridge`)

### Connection Testing
The server is configured for streamable HTTP connections and responds appropriately to MCP protocol requests.

## Troubleshooting

### Common Issues and Solutions

1. **Server Not Running**:
   ```bash
   # Check if server is running
   ps aux | grep mcp-chrome-bridge
   
   # Restart if needed
   npm install -g mcp-chrome-bridge
   ```

2. **Port 12306 Conflicts**:
   - Check what's using the port: `lsof -i :12306`
   - Kill conflicting processes if necessary

3. **Chrome Extension Not Loading**:
   - Ensure Developer mode is enabled in Chrome
   - Check that the extension directory path is correct
   - Look for errors in Chrome's extension management page

4. **Connection Issues**:
   - Verify the MCP server URL in Augment Code configuration
   - Check Chrome extension connection status
   - Ensure no firewall blocking localhost connections

### Log Locations
- Chrome extension logs: Available in Chrome DevTools for the extension
- MCP server logs: Check terminal output where the server was started

## Usage Examples

Once configured, you can use natural language commands with Augment Code such as:
- "Take a screenshot of the current page"
- "Navigate to GitHub and search for mcp-chrome"
- "Find all clickable buttons on this page"
- "Capture network requests while I browse"
- "Search my browser history for Python tutorials"
- "Add this page to my bookmarks"

## Security Considerations

- The MCP server runs locally on localhost only
- Chrome extension has necessary permissions for browser automation
- All data processing happens locally
- No external services are contacted without explicit user action

## Maintenance

### Updating
- **MCP Bridge**: `npm update -g mcp-chrome-bridge`
- **Chrome Extension**: Download latest release from GitHub and replace files

### Monitoring
- Check server status: `ps aux | grep mcp-chrome-bridge`
- Monitor port usage: `netstat -tlnp | grep 12306`

## Support Resources

- **GitHub Repository**: https://github.com/hangwin/mcp-chrome
- **Documentation**: Available in the `docs/` directory of the repository
- **Issues**: Report problems on the GitHub issues page

---

**Installation completed successfully on**: $(date)
**System**: Linux (Ubuntu/Mint)
**Node.js Version**: v20.19.4
**NPM Version**: 10.8.2
**MCP-Chrome Bridge Version**: 1.0.29
**Chrome Extension Version**: 0.0.6
